import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  RefreshControl,
  Dimensions,
  Platform
} from 'react-native';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Devi<PERSON> } from 'react-native-ble-plx';
import { Line<PERSON>hart } from 'react-native-chart-kit';
import { request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import { Buffer } from 'buffer';

const { width: screenWidth } = Dimensions.get('window');

const LIFEBOLT_CONFIG = {
  SERVICE_UUID: '425a3939-0e5b-2b01-c199-8e576d208e93', // lower case
  CHARACTERISTICS: {
    READ_WRITE: '49535343-6daa-4d02-abf6-19569aca69fe',
    WRITE_NOTIFY_1: '49535343-aca3-481c-91ec-d85e28a60318',
    RW_NOTIFY: '49535343-1e4d-4bd9-ba61-23c647249616',
    WRITE_ONLY: '*************-43f4-a8d4-ecbe34729bb3',
    WRITE_NOTIFY_2: '49535343-026e-3a9b-954c-97daef17e26e',
  },
};

const VITAL_COMMANDS = {
  TEMPERATURE: 109, // 'm'
  SPO2: 112,        // 'p'
  BP: 98,           // 'b'
  BLOOD_GLUCOSE: 103, // 'g'
  ECG: 101,         // 'e'
  STETH_START: 115, // 's'
  TORCH_ON: 116,    // 't'
  CANCEL: 120,      // 'x'
};

export default function App() {
  // BleManager singleton using useRef to avoid multiple instances
  const managerRef = useRef(new BleManager());
  const manager = managerRef.current;

  // BLE & Device state
  const [isScanning, setIsScanning] = useState(false);
  const [devices, setDevices] = useState([]);
  const [device, setDevice] = useState(null);
  const [isConnected, setIsConnected] = useState(false);

  // Vitals state
  const [vitals, setVitals] = useState({
    heartRate: null,
    spo2: null,
    systolicBP: null,
    diastolicBP: null,
    temperatureC: null,
    temperatureF: null,
    timestamp: null,
  });

  // ECG waveform data
  const [ecgData, setEcgData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // To store subscriptions for characteristics notifications
  const notificationSubscriptions = useRef([]);

  useEffect(() => {
    // Request permissions when app loads
    requestPermissions();

    // Cleanup BLE and subscription on unmount
    return () => {
      stopScan();
      removeNotificationListeners();
      disconnectDevice();
      manager.destroy();
    };
  }, []);

  // ********************** Permissions **********************
  async function requestPermissions() {
    if (Platform.OS === 'android') {
      const permissions = [
        PERMISSIONS.ANDROID.BLUETOOTH_SCAN,
        PERMISSIONS.ANDROID.BLUETOOTH_CONNECT,
        PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
      ];

      for (const permission of permissions) {
        const result = await request(permission);
        if (result !== RESULTS.GRANTED) {
          Alert.alert('Permission Required', `${permission} is required for BLE functionality.`);
          return;
        }
      }
    }
  }

  // ********************** Scanning **********************
  function startScan() {
    if (isScanning) return;
    setDevices([]);
    setIsScanning(true);

    // Stop any existing scan first
    if (manager.isDeviceScanning()) {
      manager.stopDeviceScan();
    }

    console.log('Starting BLE scan...');
    manager.startDeviceScan([LIFEBOLT_CONFIG.SERVICE_UUID], null, (error, scannedDevice) => {
      if (error) {
        console.error('Scan error:', error);
        setIsScanning(false);
        return;
      }

      if (scannedDevice && scannedDevice.name && scannedDevice.name.toLowerCase().includes('lifebolt')) {
        setDevices(prevDevices => {
          if (!prevDevices.find(d => d.id === scannedDevice.id)) {
            console.log(`Discovered device: ${scannedDevice.name} (${scannedDevice.id})`);
            return [...prevDevices, scannedDevice];
          }
          return prevDevices;
        });
      }
    });

    // Automatically stop scan after 10 seconds
    setTimeout(() => {
      stopScan();
    }, 10000);
  }

  function stopScan() {
    if (manager.isDeviceScanning()) {
      manager.stopDeviceScan();
      console.log('Stopped BLE scan.');
    }
    setIsScanning(false);
  }

  // ********************** Connection **********************
  async function connectToDevice(selectedDevice) {
    setIsLoading(true);
    try {
      if (manager.isDeviceScanning()) {
        manager.stopDeviceScan();
        setIsScanning(false);
      }
      const connectedDevice = await selectedDevice.connect();
      setDevice(connectedDevice);
      setIsConnected(true);

      await connectedDevice.discoverAllServicesAndCharacteristics();

      // Setup notifications, clear previous subs first
      removeNotificationListeners();

      await setupNotifications(connectedDevice);

      Alert.alert('Connected', `Connected to device: ${connectedDevice.name || connectedDevice.id}`);

      // Listen for device disconnect event
      manager.onDeviceDisconnected(connectedDevice.id, (error, device) => {
        if (error) {
          console.warn('Device disconnected with error:', error.message);
        } else {
          console.log('Device disconnected:', device?.name);
        }
        cleanupConnection();
        Alert.alert('Disconnected', 'Device disconnected');
      });
    } catch (error) {
      console.error('Connection error:', error.message || error);
      Alert.alert('Connection Error', error.message || 'Failed to connect');
    } finally {
      setIsLoading(false);
    }
  }

  async function disconnectDevice() {
    if (!device) return;
    try {
      await device.cancelConnection();
    } catch (error) {
      console.warn('Error during disconnect:', error);
    }
    cleanupConnection();
  }

  function cleanupConnection() {
    setDevice(null);
    setIsConnected(false);
    setVitals({
      heartRate: null,
      spo2: null,
      systolicBP: null,
      diastolicBP: null,
      temperatureC: null,
      temperatureF: null,
      timestamp: null,
    });
    setEcgData([]);
    removeNotificationListeners();
  }

  // ********************** Notifications **********************
  async function setupNotifications(connectedDevice) {
    // Subscribe to all notify-capable characteristics
    const notifyUUIDs = [
      LIFEBOLT_CONFIG.CHARACTERISTICS.WRITE_NOTIFY_1,
      LIFEBOLT_CONFIG.CHARACTERISTICS.RW_NOTIFY,
      LIFEBOLT_CONFIG.CHARACTERISTICS.WRITE_NOTIFY_2,
    ];

    for (const charUuid of notifyUUIDs) {
      try {
        const subscription = connectedDevice.monitorCharacteristicForService(
          LIFEBOLT_CONFIG.SERVICE_UUID,
          charUuid,
          (error, characteristic) => {
            if (error) {
              console.warn(`Notification error on ${charUuid}:`, error.message);
              return;
            }
            if (characteristic?.value) {
              handleNotification(characteristic.value);
            }
          }
        );
        notificationSubscriptions.current.push(subscription);
      } catch (error) {
        console.log(`Failed to monitor characteristic ${charUuid}:`, error);
      }
    }
  }

  function removeNotificationListeners() {
    notificationSubscriptions.current.forEach(sub => sub.remove());
    notificationSubscriptions.current = [];
  }

  // ********************** Notification Data Parsing **********************
  function handleNotification(base64Value) {
    try {
      // Decode base64 to Uint8Array using Buffer
      const buffer = Buffer.from(base64Value, 'base64');
      const data = new Uint8Array(buffer);
      const timestamp = new Date();

      // Try parse text first
      const textDecoder = new TextDecoder('ascii');
      const textData = textDecoder.decode(data);
      parseVitalsData(textData, timestamp);
    } catch (error) {
      console.error('Failed to handle notification:', error);
    }
  }

  function parseVitalsData(textData, timestamp) {
    try {
      let updatedVitals = { ...vitals, timestamp };
      let updateNeeded = false;

      if (textData.startsWith('T_') && textData.endsWith('_#')) {
        // Temperature: T_35.9_96.6_#
        const parts = textData.slice(2, -2).split('_');
        const tempC = parseFloat(parts[0]);
        if (!isNaN(tempC) && tempC >= 30 && tempC <= 45) {
          updatedVitals.temperatureC = tempC;
          updateNeeded = true;
        }
        if (parts.length > 1) {
          const tempF = parseFloat(parts[1]);
          if (!isNaN(tempF)) {
            updatedVitals.temperatureF = tempF;
          }
        }
      } else if (textData.startsWith('O_') && textData.includes('_#')) {
        // SpO2 & Heart Rate: O_98_72_#
        const parts = textData.slice(2, -2).split('_');
        const spo2 = parseInt(parts[0], 10);
        if (!isNaN(spo2) && spo2 >= 50 && spo2 <= 100) {
          updatedVitals.spo2 = spo2;
          updateNeeded = true;
        }
        if (parts.length > 1) {
          const hr = parseInt(parts[1], 10);
          if (!isNaN(hr) && hr >= 40 && hr <= 200) {
            updatedVitals.heartRate = hr;
          }
        }
      } else if (textData.startsWith('B_') && textData.includes('_#')) {
        // Blood Pressure: B_120_80_#
        const parts = textData.slice(2, -2).split('_');
        const sys = parseInt(parts[0], 10);
        const dia = parseInt(parts[1], 10);
        if (!isNaN(sys) && !isNaN(dia) && sys >= 80 && sys <= 250 && dia >= 40 && dia <= 150) {
          updatedVitals.systolicBP = sys;
          updatedVitals.diastolicBP = dia;
          updateNeeded = true;
        }
      } else if (textData.startsWith('E_') && textData.includes('_#')) {
        // ECG data: E_[values]_#
        const ecgContent = textData.slice(2, -2);
        parseECGData(ecgContent);
      }

      if (updateNeeded) {
        setVitals(updatedVitals);
      }
    } catch (error) {
      console.error('Error parsing vitals data:', error);
    }
  }

  function parseECGData(ecgContent) {
    try {
      let voltageValues = [];
      let heartRate = null;

      if (ecgContent.includes(',')) {
        voltageValues = ecgContent.split(',').map(v => parseFloat(v.trim())).filter(v => !isNaN(v));
      } else if (ecgContent.includes(' ')) {
        voltageValues = ecgContent.split(' ').map(v => parseFloat(v.trim())).filter(v => !isNaN(v));
      } else if (/\d+/.test(ecgContent)) {
        if (ecgContent.toUpperCase().includes('HR') || ecgContent.toUpperCase().includes('BPM')) {
          const hrMatch = ecgContent.match(/(\d{2,3})/);
          if (hrMatch) heartRate = parseInt(hrMatch[1]);
        } else {
          const v = parseFloat(ecgContent);
          if (!isNaN(v)) voltageValues = [v];
        }
      }

      if (voltageValues.length > 0) {
        setEcgData(prev => {
          const combined = [...prev, ...voltageValues];
          if (combined.length > 100) return combined.slice(combined.length - 100);
          return combined;
        });
      }

      if (heartRate !== null) {
        setVitals(prev => ({ ...prev, heartRate }));
      }
    } catch (error) {
      console.error('Error parsing ECG data:', error);
    }
  }

  // ********************** Commands **********************
  async function sendVitalCommand(key) {
    if (!device || !isConnected) {
      Alert.alert('Error', 'No device connected');
      return;
    }
    try {
      const byteVal = VITAL_COMMANDS[key];
      if (!byteVal) {
        Alert.alert('Error', 'Invalid command');
        return;
      }

      const commandBase64 = Buffer.from([byteVal]).toString('base64');

      await device.writeCharacteristicWithResponseForService(
        LIFEBOLT_CONFIG.SERVICE_UUID,
        LIFEBOLT_CONFIG.CHARACTERISTICS.RW_NOTIFY,
        commandBase64
      );
      Alert.alert('Command Sent', `Command ${key} sent`);
    } catch (error) {
      Alert.alert('Command Error', error.message || 'Failed to send command');
    }
  }

  // ********************** Render UI **********************
  return (
    <ScrollView
      contentContainerStyle={styles.container}
      refreshControl={<RefreshControl refreshing={isLoading} onRefresh={() => {}} />}
    >
      <Text style={styles.title}>Lifebolt Vitals Monitor</Text>

      {/* Connection Status */}
      <View style={[styles.section, isConnected ? styles.connected : styles.disconnected]}>
        <Text style={styles.sectionTitle}>Connection Status</Text>
        <Text style={styles.statusText}>
          {isConnected ? `Connected to: ${device?.name || device?.id}` : 'Disconnected'}
        </Text>
        {isConnected && (
          <TouchableOpacity style={[styles.button, styles.disconnectButton]} onPress={disconnectDevice}>
            <Text style={styles.buttonText}>Disconnect</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Devices List for scanning */}
      {!isConnected && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Available Lifebolt Devices</Text>
          <TouchableOpacity
            style={[styles.button, isScanning && styles.buttonDisabled]}
            onPress={startScan}
            disabled={isScanning}
          >
            <Text style={styles.buttonText}>{isScanning ? 'Scanning...' : 'Scan Devices'}</Text>
          </TouchableOpacity>

          {devices.map(dev => (
            <TouchableOpacity
              key={dev.id}
              style={styles.deviceItem}
              onPress={() => connectToDevice(dev)}
              disabled={isLoading}
            >
              <Text style={styles.deviceName}>{dev.name}</Text>
              <Text style={styles.deviceAddress}>{dev.id}</Text>
            </TouchableOpacity>
          ))}

          {devices.length === 0 && !isScanning && (
            <Text style={{ textAlign: 'center', marginTop: 10, color: '#666' }}>
              No devices found. Make sure your Lifebolt device is powered on and advertising.
            </Text>
          )}
        </View>
      )}

      {/* Vitals Display */}
      {isConnected && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Latest Vitals</Text>
          <View style={styles.vitalsGrid}>
            <View style={styles.vitalCard}>
              <Text style={styles.vitalLabel}>Heart Rate</Text>
              <Text style={styles.vitalValue}>{vitals.heartRate ?? '--'} BPM</Text>
            </View>
            <View style={styles.vitalCard}>
              <Text style={styles.vitalLabel}>SpO2</Text>
              <Text style={styles.vitalValue}>{vitals.spo2 ?? '--'} %</Text>
            </View>
            <View style={styles.vitalCard}>
              <Text style={styles.vitalLabel}>Blood Pressure</Text>
              <Text style={styles.vitalValue}>
                {vitals.systolicBP && vitals.diastolicBP
                  ? `${vitals.systolicBP} / ${vitals.diastolicBP}`
                  : '--'}
              </Text>
            </View>
            <View style={styles.vitalCard}>
              <Text style={styles.vitalLabel}>Temperature</Text>
              <Text style={styles.vitalValue}>
                {vitals.temperatureC ? `${vitals.temperatureC} °C` : '--'}
              </Text>
            </View>
          </View>
          {vitals.timestamp && (
            <Text style={styles.timestamp}>
              Last Updated: {vitals.timestamp.toLocaleTimeString()}
            </Text>
          )}
        </View>
      )}

      {/* Commands for measurements */}
      {isConnected && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Commands</Text>
          <View style={styles.commandGrid}>
            {Object.keys(VITAL_COMMANDS).map((key) => (
              <TouchableOpacity
                key={key}
                style={[
                  styles.commandButton,
                  key === 'CANCEL' ? styles.cancelButton : null,
                ]}
                onPress={() => sendVitalCommand(key)}
              >
                <Text style={styles.commandButtonText}>{key}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* ECG Line Chart */}
      {isConnected && ecgData.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>ECG Waveform</Text>
          <LineChart
            data={{
              labels: [],
              datasets: [
                {
                  data: ecgData.slice(-50), // Last 50 samples
                  color: (opacity = 1) => `rgba(255,0,0,${opacity})`,
                  strokeWidth: 2,
                },
              ],
            }}
            width={screenWidth - 40}
            height={200}
            chartConfig={{
              backgroundGradientFrom: '#fff',
              backgroundGradientTo: '#fff',
              color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
              labelColor: () => '#666',
              decimalPlaces: 1,
              propsForDots: { r: '0' },
            }}
            withDots={false}
            withInnerLines={true}
            bezier
            style={styles.chart}
          />
        </View>
      )}

      {isLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Processing...</Text>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    paddingBottom: 40,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    alignSelf: 'center',
    marginBottom: 20,
    color: '#333',
  },
  section: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#fff',
    borderRadius: 10,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  connected: {
    borderColor: '#4CAF50',
    borderWidth: 1,
    backgroundColor: '#e8f5e9',
  },
  disconnected: {
    borderColor: '#f44336',
    borderWidth: 1,
    backgroundColor: '#fff0f0',
  },
  button: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
    marginBottom: 12,
  },
  buttonDisabled: {
    backgroundColor: '#8ebfff',
  },
  buttonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  disconnectButton: {
    backgroundColor: '#FF3B30',
  },
  deviceItem: {
    marginBottom: 10,
    backgroundColor: '#f7f7f7',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  deviceName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  deviceAddress: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  vitalsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  vitalCard: {
    width: '48%',
    backgroundColor: '#f0f4f8',
    borderRadius: 8,
    paddingVertical: 20,
    alignItems: 'center',
    marginBottom: 10,
  },
  vitalLabel: {
    fontSize: 14,
    color: '#555',
  },
  vitalValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 4,
  },
  timestamp: {
    fontSize: 12,
    color: '#777',
    textAlign: 'center',
    marginTop: 8,
  },
  commandGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  commandButton: {
    width: '48%',
    backgroundColor: '#34C759',
    paddingVertical: 14,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#FF3B30',
    width: '100%',
  },
  commandButtonText: {
    color: '#fff',
    fontWeight: '700',
    fontSize: 16,
  },
  chart: {
    borderRadius: 12,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    right: 0,
    left: 0,
    backgroundColor: 'rgba(0,0,0,0.35)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#fff',
    fontSize: 16,
  },
});
